<script setup lang="ts">
import type { VxeTablePropTypes } from 'vxe-table';

import type { HerbalDrugItem, HerbalPrescriptionData } from './typing';

import type { DrugVO } from '#/api/clinic/pharmacy/drug/model';

import { computed, ref, watch } from 'vue';

import { buildShortUUID } from '@vben/utils';

import { Button, Input, Select, Switch } from 'ant-design-vue';

import { DrugSelect } from '#/components/clinic/drug-select';
import { QiliDictEnum } from '#/constants/qili-dict-enum';
import { validTableEditRules } from '#/utils/cis-common';
import { getDictOptions } from '#/utils/dict';

const props = withDefaults(defineProps<Props>(), {
  maxHeight: '250px',
  disabled: false,
  modelValue: () => ({
    tableData: [],
    prescription: '',
    dosage: 1,
    frequency: 'tid',
    usage: '水煎',
    usageValue: 0,
    isDecoction: 0,
  }),
});

const emit = defineEmits<{
  change: [value: HerbalPrescriptionData];
  'update:modelValue': [value: HerbalPrescriptionData];
}>();

const { Option } = Select;

const frequencyOptions = computed(() => {
  return getDictOptions(QiliDictEnum.QILI_DRUG_FREQUENCY);
});

const usageOptions = computed(() => {
  return getDictOptions(QiliDictEnum.QILI_TCM_USAGE);
});

interface Props {
  maxHeight?: string;
  modelValue?: HerbalPrescriptionData;
  disabled?: boolean;
}

// 初始化本地数据（中草药）
const tableData = ref<HerbalDrugItem[]>(props.modelValue?.tableData || []);
const prescription = ref(props.modelValue?.prescription || '');
const dosage = ref(props.modelValue?.dosage || 0);
const frequency = ref(props.modelValue?.frequency || 'tid');
const usage = ref(props.modelValue?.usage || '水煎');
const usageValue = ref(props.modelValue?.usageValue || 0);
const isDecoction = ref(props.modelValue?.isDecoction || 0);

// 监听props变化，更新本地数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      tableData.value = newValue.tableData || [];
      prescription.value = newValue.prescription || '';
      dosage.value = newValue.dosage || 0;
      frequency.value = newValue.frequency || 'tid';
      usage.value = newValue.usage || '水煎';
      usageValue.value = newValue.usageValue || 0;
      isDecoction.value = newValue.isDecoction || 0;
    }
  },
  { deep: true },
);

// 计算当前完整数据
const currentData = computed(
  (): HerbalPrescriptionData => ({
    tableData: tableData.value,
    prescription: prescription.value,
    dosage: dosage.value,
    frequency: frequency.value,
    usage: usage.value,
    usageValue: usageValue.value,
    isDecoction: isDecoction.value,
  }),
);

// 发送数据变化
const emitChange = () => {
  const data = currentData.value;
  emit('update:modelValue', data);
  emit('change', data);
};

// 监听tableData变化，更新价格计算
watch(
  tableData,
  (newTableData) => {
    newTableData.forEach((item) => {
      item.subtotal = (Number(item.price) || 0) * (Number(item.amount) || 0);
    });
  },
  { deep: true },
);

// 监听各个字段变化，发送到父组件
watch(
  [tableData, prescription, dosage, frequency, usage, usageValue, isDecoction],
  () => {
    emitChange();
  },
  { deep: true },
);

const editConfig = computed<VxeTablePropTypes.EditConfig>(() => ({
  trigger: 'click',
  mode: 'cell',
  autoFocus: true,
  showStatus: true,
  showUpdateStatus: true,
  showInsertStatus: true,
  enabled: !props.disabled,
}));

const keyboardConfig = computed<VxeTablePropTypes.KeyboardConfig>(() => ({
  isEdit: true, // 进入/切换编辑
  isTab: true, // Tab 导航
  isEnter: true, // Enter 导航
  isArrow: true, // 方向键导航
  isDel: true, // 删除键
  isEsc: true, // ESC键退出编辑
}));

// vxe 校验规则
const editRules = ref<VxeTablePropTypes.EditRules>({
  name: [{ required: true, message: '请选择药品', trigger: 'change' }],
  amount: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    {
      validator({ cellValue }) {
        const num = Number(cellValue);
        if (Number.isNaN(num) || num <= 0) {
          return new Error('数量必须大于 0');
        }
      },
      trigger: 'blur',
    },
  ],
});

// 表实例与对外暴露校验方法
const tableRef = ref();

// 可编辑的列字段
const editableFields = ['name', 'amount'];

// 键盘导航处理
const handleKeyDown = ({ $event, row, column }: any) => {
  if (!row || !column) return;

  const { keyCode } = $event;
  const currentRowIndex = tableData.value.findIndex(
    (item) => item.id === row.id,
  );
  const currentFieldIndex = editableFields.findIndex(
    (field) => field === column.field,
  );

  // 只处理方向键: 37-左, 38-上, 39-右, 40-下
  if ([37, 38, 39, 40].includes(keyCode)) {
    $event.preventDefault();
    $event.stopPropagation();

    let targetRowIndex = currentRowIndex;
    let targetFieldIndex = currentFieldIndex;

    switch (keyCode) {
      case 37: // ArrowLeft
        // 左键：切换到前一个可编辑列
        targetFieldIndex =
          currentFieldIndex > 0
            ? currentFieldIndex - 1
            : editableFields.length - 1;
        break;
      case 39: // ArrowRight
        // 右键：切换到下一个可编辑列
        targetFieldIndex =
          currentFieldIndex < editableFields.length - 1
            ? currentFieldIndex + 1
            : 0;
        break;
      case 38: // ArrowUp
        // 上键：切换到上一行
        if (currentRowIndex > 0) {
          targetRowIndex = currentRowIndex - 1;
        }
        break;
      case 40: // ArrowDown
        // 下键：切换到下一行，如果是最后一行则新增行
        if (currentRowIndex < tableData.value.length - 1) {
          targetRowIndex = currentRowIndex + 1;
        } else {
          // 最后一行，新增一行
          addRow();
          // 等待DOM更新后再设置目标行索引
          setTimeout(() => {
            const newTargetRowIndex = tableData.value.length - 1;
            const targetField = editableFields[targetFieldIndex];
            const targetRow = tableData.value[newTargetRowIndex];

            if (targetRow && targetField) {
              tableRef.value.setActiveCell(targetRow, targetField);
            }
          }, 100);
          return; // 提前返回，避免执行后面的setTimeout
        }
        break;
    }

    // 延迟执行，确保DOM更新完成
    setTimeout(() => {
      const targetField = editableFields[targetFieldIndex];
      const targetRow = tableData.value[targetRowIndex];

      if (targetRow && targetField) {
        // 激活目标单元格编辑状态
        tableRef.value.setActiveCell(targetRow, targetField);
      }
    }, 50);
  }
};

const validate = async () => {
  // 没有数据则跳过校验，认为通过
  if (!tableData.value || tableData.value.length === 0) {
    return true;
  }
  try {
    const tableErrMap = await tableRef.value.validate(true);
    return validTableEditRules(tableErrMap, '中药处方');
  } catch {
    return false;
  }
};

const addRow = () => {
  tableData.value.push({
    id: buildShortUUID(),
    name: '',
    code: '-',
    price: 0,
    unit: '',
    amount: 1,
    subtotal: 0,
  });
};

// 处理中草药选择
const handleDrugSelect = (drug: DrugVO, row: HerbalDrugItem) => {
  row.id = drug.id;
  row.name = drug.name;
  row.code = drug.code;
  row.price = Number(drug.price) || 0;
  row.unit = drug.unit;
  row.subtotal = (Number(drug.price) || 0) * (Number(row.amount) || 0);
};

const removeRow = (row: HerbalDrugItem) => {
  const index = tableData.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    tableData.value.splice(index, 1);
  }
};

// 单剂重量（数值）
const singleDoseWeightValue = computed(() => {
  return tableData.value.reduce(
    (sum, item) => sum + (Number(item.amount) || 0),
    0,
  );
});

// 单剂重量（显示用字符串）
const singleDoseWeight = computed(() => {
  return singleDoseWeightValue.value.toFixed(2);
});

// 总重量 = 单剂重量 × 剂数
const totalWeight = computed(() => {
  return (singleDoseWeightValue.value * (Number(dosage.value) || 0)).toFixed(2);
});

const totalPrice = computed(() => {
  return tableData.value
    .reduce((sum, item) => sum + (Number(item.subtotal) || 0), 0)
    .toFixed(2);
});

// 总价格（包含剂数）
const totalPriceWithDosage = computed(() => {
  return (Number(totalPrice.value) * Number(dosage.value)).toFixed(2);
});

const drugCount = computed(() => {
  return tableData.value.length;
});

// 清空表单数据
const clearData = () => {
  tableData.value = [];
  prescription.value = '';
  dosage.value = 1;
  frequency.value = 'tid';
  usage.value = '水煎';
  usageValue.value = 0;
  isDecoction.value = 0;
};

// 设置表单数据
const setData = (data: Partial<HerbalPrescriptionData>) => {
  if (data.tableData !== undefined) tableData.value = data.tableData;
  if (data.prescription !== undefined) prescription.value = data.prescription;
  if (data.dosage !== undefined) dosage.value = data.dosage;
  if (data.frequency !== undefined) frequency.value = data.frequency;
  if (data.usage !== undefined) usage.value = data.usage || '水煎';
  if (data.usageValue !== undefined) usageValue.value = data.usageValue;
  if (data.isDecoction !== undefined) isDecoction.value = data.isDecoction;
};

const getDictMapping = (): Record<string, string> => {
  return {
    usage: QiliDictEnum.QILI_TCM_USAGE,
    frequency: QiliDictEnum.QILI_DRUG_FREQUENCY,
  };
};

// 暴露方法给父组件
defineExpose({
  addRow,
  clearData,
  setData,
  getData: () => currentData.value,
  validate,
  getDictMapping,
});
</script>

<template>
  <div>
    <vxe-table
      ref="tableRef"
      :data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :valid-config="{ message: 'tooltip' }"
      border
      stripe
      size="small"
      align="center"
      :max-height="maxHeight"
      :keyboard-config="keyboardConfig"
      @keydown="handleKeyDown"
    >
      <vxe-column type="seq" width="50" title="序号" />
      <vxe-column
        field="name"
        title="药品名称"
        :edit-render="{ autoFocus: true, placeholder: '请点击选择药品...' }"
      >
        <template #edit="{ row }">
          <DrugSelect
            v-model:value="row.name"
            class="w-full"
            :disabled="props.disabled"
            :selected-drugs="
              tableData.filter((item) => item.name && item.id !== row.id)
            "
            @select="(drug) => handleDrugSelect(drug, row)"
          />
        </template>
      </vxe-column>
      <vxe-column field="code" title="药品编码" width="120" />
      <vxe-column field="price" title="单价" width="120">
        <template #default="{ row }">
          <span>￥{{ row.price.toFixed(2) }}</span>
          <span v-if="row.unit" class="ml-1 text-xs text-gray-400">
            / {{ row.unit }}
          </span>
        </template>
      </vxe-column>
      <vxe-column field="amount" title="数量" width="120" :edit-render="{}">
        <template #edit="{ row }">
          <Input
            v-model:value="row.amount"
            type="number"
            placeholder="请输入数量"
            :min="0"
            :disabled="props.disabled"
          >
            <template #suffix>
              <span v-if="row.unit" class="ml-1 text-xs text-gray-400">
                {{ row.unit }}
              </span>
            </template>
          </Input>
        </template>
        <template #default="{ row }">
          <span>{{ row.amount }}</span>
          <span v-if="row.unit" class="ml-1 text-xs text-gray-400">
            {{ row.unit }}
          </span>
        </template>
      </vxe-column>
      <vxe-column title="价格">
        <template #default="{ row }">
          <span>￥{{ (row.price * row.amount).toFixed(2) }}</span>
        </template>
      </vxe-column>
      <vxe-column v-if="!props.disabled" title="操作" width="100" fixed="right">
        <template #default="{ row }">
          <Button
            type="link"
            danger
            size="small"
            class="flex items-center"
            :disabled="props.disabled"
            @click="removeRow(row)"
          >
            <i
              class="icon-[material-symbols-light--delete-outline-rounded] mr-1"
            ></i>
            删除
          </Button>
        </template>
      </vxe-column>
    </vxe-table>

    <div
      class="mt-2 flex select-none flex-col rounded-md bg-gray-100 p-2 text-sm"
    >
      <div class="flex items-center justify-start text-sm">
        <label class="w-[3rem]">医嘱:</label>
        <Input
          v-model:value="prescription"
          placeholder="请输入医嘱"
          allow-clear
          size="small"
          class="ml-[1px] w-[50%]"
          :disabled="disabled"
        />
      </div>
      <div class="mt-2 flex items-center">
        <label class="mr-[1px] w-[3rem]">用法:</label>
        <Input
          v-model:value="dosage"
          type="number"
          :min="1"
          size="small"
          placeholder="请输入"
          class="mr-1 w-[80px] text-center font-bold"
          :disabled="disabled"
        >
          <template #suffix>
            <span class="font-normal text-gray-400">剂</span>
          </template>
        </Input>
        <Select
          v-model:value="frequency"
          placeholder="请选择"
          size="small"
          class="mr-1 w-[120px] font-bold"
          :disabled="disabled"
        >
          <Option
            v-for="item in frequencyOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </Option>
        </Select>
        <div class="mr-1 font-bold">
          <Select
            v-model:value="usage"
            placeholder="请选择"
            class="w-[100px]"
            size="small"
            :disabled="props.disabled"
          >
            <Option
              v-for="item in usageOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </Option>
          </Select>
        </div>
        <Input
          v-model:value="usageValue"
          type="number"
          :min="0"
          size="small"
          placeholder="请输入"
          class="w-[80px] text-center font-bold"
          :disabled="disabled"
        >
          <template #suffix>
            <span class="font-normal text-gray-400">ml</span>
          </template>
        </Input>
        <div class="ml-4 flex items-center justify-start text-sm">
          <label class="w-[3rem]">代煎:</label>
          <Switch
            v-model:checked="isDecoction"
            size="small"
            checked-children="是"
            un-checked-children="否"
            :checked-value="1"
            :un-checked-value="0"
            :disabled="props.disabled"
          />
        </div>
      </div>
    </div>
    <!-- <Divider style="margin: 12px 0" /> -->
    <div class="mt-4 flex select-none justify-end text-xs">
      <div class="mr-2 flex text-gray-400">
        <label>单剂重量：</label>
        <div class="text-primary">
          {{ singleDoseWeight }}
        </div>
        <span class="ml-1">g</span>
      </div>
      <div class="mr-2 flex text-gray-400">
        <label>总重量：</label>
        <div class="text-primary">
          {{ totalWeight }}
        </div>
        <span class="ml-1">g</span>
      </div>

      <div class="mr-2 flex text-gray-400">
        <label>药品数量：</label>
        <div class="text-primary">
          {{ drugCount }}
        </div>
        <span class="ml-1">味</span>
      </div>
      <div class="flex text-gray-400">
        <label>总价：</label>
        <div class="text-primary">
          {{ totalPriceWithDosage }}
        </div>
        <span class="ml-1">元</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.vxe-table) {
  border-radius: 8px;
}

/* 确保输入框内容居中 */
:deep(.ant-input-number-input) {
  text-align: center;
}
</style>

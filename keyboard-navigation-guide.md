# VXE-Table 键盘导航功能说明

## 功能概述

为 `TcmPrescriptionTable` 组件添加了键盘导航功能，支持使用方向键在表格的可编辑单元格之间快速切换。

## 功能特性

### 1. 左右键导航
- **左键 (←)**: 切换到当前行的前一个可编辑单元格
- **右键 (→)**: 切换到当前行的下一个可编辑单元格
- 支持循环切换：在第一个单元格按左键会跳到最后一个单元格，在最后一个单元格按右键会跳到第一个单元格

### 2. 上下键导航
- **上键 (↑)**: 切换到上一行的相同列
- **下键 (↓)**: 切换到下一行的相同列

### 3. 自动新增行
- 当在最后一行按下键时，会自动新增一行并将焦点移动到新行的相应单元格

## 可编辑字段

当前支持键盘导航的字段：
- `name`: 药品名称
- `amount`: 数量

## 使用方法

1. 点击任意可编辑单元格进入编辑状态
2. 使用方向键在单元格之间导航：
   - 左右键：在同一行的可编辑单元格间切换
   - 上下键：在不同行的相同列间切换
   - 在最后一行按下键：自动新增一行

## 技术实现

### 键盘配置
```typescript
const keyboardConfig = computed<VxeTablePropTypes.KeyboardConfig>(() => ({
  isEdit: true,     // 进入/切换编辑
  isTab: true,      // Tab 导航
  isEnter: true,    // Enter 导航
  isArrow: true,    // 方向键导航
  isDel: true,      // 删除键
  isEsc: true,      // ESC键退出编辑
}));
```

### 事件处理
- 监听 `@keydown` 事件
- 处理键码：37(左)、38(上)、39(右)、40(下)
- 使用 `setActiveCell` 方法激活目标单元格

### 自动新增行逻辑
```typescript
// 在最后一行按下键时
if (currentRowIndex === tableData.value.length - 1 && keyCode === 40) {
  addRow(); // 添加新行
  // 延迟设置焦点到新行
  setTimeout(() => {
    tableRef.value.setActiveCell(newRow, targetField);
  }, 100);
}
```

## 注意事项

1. 只有在编辑状态下才能使用键盘导航
2. 方向键会阻止默认行为，专门用于单元格导航
3. 新增行后会有短暂延迟以确保DOM更新完成
4. 键盘导航只在可编辑字段间生效

## 扩展说明

如需添加更多可编辑字段的键盘导航支持，只需在 `editableFields` 数组中添加相应的字段名：

```typescript
const editableFields = ['name', 'amount', 'newField'];
```

确保对应的列配置了 `:edit-render` 属性以支持编辑功能。
